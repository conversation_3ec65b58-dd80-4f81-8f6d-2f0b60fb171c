#include "stm32f1xx_hal.h"
#include <stdint.h>
#include <stdbool.h>

/*** V9260S UART Configuration ***/
#define V9260S_UART &huart1  // Change to your UART handle
#define V9260S_TIMEOUT 100   // Timeout in milliseconds

/*** V9260S Registers ***/
#define REG_U_RMS 0x00CF     // Voltage RMS
#define REG_I_RMS 0x00CE     // Current RMS
#define REG_P_ACTIVE 0x00CC  // Active Power
#define REG_Q_REACTIVE 0x00CD // Reactive Power

/*** Helper Macros ***/
#define BYTE_COUNT 8 // Command frame size

/*** Function Prototypes ***/
void V9260S_Init(void);
bool V9260S_ReadRegister(uint16_t reg, uint32_t *value);
float V9260S_GetVoltage(void);
float V9260S_GetCurrent(void);
float V9260S_GetActivePower(void);
float V9260S_CalculatePowerFactor(void);

/*** Implementation ***/

void V9260S_Init(void) {
    // Initialization code if necessary, e.g., GPIO, UART setup
    HAL_UART_Init(V9260S_UART);
}

bool V9260S_ReadRegister(uint16_t reg, uint32_t *value) {
    uint8_t txFrame[BYTE_COUNT] = {0x7D, (reg >> 8) & 0xFF, reg & 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00};
    uint8_t rxFrame[BYTE_COUNT] = {0};
    
    // Calculate Checksum
    uint8_t checksum = 0;
    for (int i = 0; i < 7; i++) {
        checksum += txFrame[i];
    }
    txFrame[7] = ~checksum + 0x33;

    // Send frame
    if (HAL_UART_Transmit(V9260S_UART, txFrame, BYTE_COUNT, V9260S_TIMEOUT) != HAL_OK) {
        return false;
    }

    // Receive frame
    if (HAL_UART_Receive(V9260S_UART, rxFrame, BYTE_COUNT, V9260S_TIMEOUT) != HAL_OK) {
        return false;
    }

    // Extract data and verify checksum
    uint8_t rxChecksum = 0;
    for (int i = 0; i < 7; i++) {
        rxChecksum += rxFrame[i];
    }
    rxChecksum = ~rxChecksum + 0x33;

    if (rxChecksum != rxFrame[7]) {
        return false; // Checksum mismatch
    }

    // Combine 4 bytes of data
    *value = (rxFrame[3] << 24) | (rxFrame[4] << 16) | (rxFrame[5] << 8) | rxFrame[6];
    return true;
}

float V9260S_GetVoltage(void) {
    uint32_t rawVoltage;
    if (V9260S_ReadRegister(REG_U_RMS, &rawVoltage)) {
        return (float)rawVoltage / 1000.0; // Convert to volts (example scaling)
    }
    return 0.0f; // Error case
}

float V9260S_GetCurrent(void) {
    uint32_t rawCurrent;
    if (V9260S_ReadRegister(REG_I_RMS, &rawCurrent)) {
        return (float)rawCurrent / 1000.0; // Convert to amps (example scaling)
    }
    return 0.0f; // Error case
}

float V9260S_GetActivePower(void) {
    uint32_t rawPower;
    if (V9260S_ReadRegister(REG_P_ACTIVE, &rawPower)) {
        return (float)rawPower / 1000.0; // Convert to watts (example scaling)
    }
    return 0.0f; // Error case
}

float V9260S_CalculatePowerFactor(void) {
    float voltage = V9260S_GetVoltage();
    float current = V9260S_GetCurrent();
    float power = V9260S_GetActivePower();

    if (voltage > 0 && current > 0) {
        return power / (voltage * current);
    }
    return 0.0f; // Error case
}
